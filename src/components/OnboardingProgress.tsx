import { cn } from '@/lib/utils';

interface OnboardingProgressProps {
  currentStep: number;
  totalSteps?: number;
  className?: string;
}

export const OnboardingProgress = ({ 
  currentStep, 
  totalSteps = 4,
  className 
}: OnboardingProgressProps) => {
  return (
    <div className={cn(
      "flex items-center justify-center py-8 bg-white/80 backdrop-blur-sm border-b",
      className
    )}>
      <div className="flex items-center space-x-2">
        {Array.from({ length: totalSteps }, (_, index) => {
          const stepNumber = index + 1;
          const isActive = stepNumber <= currentStep;
          const isLast = stepNumber === totalSteps;
          
          return (
            <div key={stepNumber} className="flex items-center">
              <div className={cn(
                "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",
                isActive 
                  ? "bg-primary text-primary-foreground" 
                  : "bg-gray-300 text-gray-500"
              )}>
                {stepNumber}
              </div>
              {!isLast && (
                <div className={cn(
                  "w-16 h-0.5",
                  stepNumber < currentStep ? "bg-primary" : "bg-gray-300"
                )} />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};