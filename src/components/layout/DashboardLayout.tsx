import React from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import {
  Bot,
  LayoutDashboard,
  MessageSquare,
  LogOut,
  User,
  ChevronDown,
  Settings,
  CreditCard,
  FileText,
  Phone,
  Mail,
  Mic,
  Workflow,
  Database,
  GitBranch,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useTranslation } from "@/hooks/useTranslation";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarProvider,
  SidebarRail,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { useAuth } from "@/context/AuthContext.tsx";
import { useProject } from "@/context/ProjectContext";

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export const DashboardLayout = ({ children }: DashboardLayoutProps) => {
  const { user, logout, isLoading } = useAuth();
  const { currentProject, projects, setCurrentProject } = useProject();
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  // 菜单数据结构
  const menuData = [
    {
      title: t("dashboard"),
      url: "/dashboard",
      icon: LayoutDashboard,
    },
    {
      title: t("channels"),
      icon: MessageSquare,
      items: [
        {
          title: t("chatbot"),
          url: "/chats",
          icon: Bot,
        },
        {
          title: t("whatsapp"),
          url: "/channels/whatsapp",
          icon: Phone,
        },
        {
          title: t("voice"),
          url: "/channels/voice",
          icon: Mic,
        },
        {
          title: t("mail"),
          url: "/channels/mail",
          icon: Mail,
        },
      ],
    },
    {
      title: t("knowledgeBase"),
      url: "/knowledge",
      icon: Database,
    },
    {
      title: t("workflow"),
      icon: Workflow,
      items: [
        {
          title: t("form"),
          url: "/form-builder",
          icon: FileText,
        },
        {
          title: t("pipeline"),
          url: "/workflow/pipeline",
          icon: GitBranch,
        },
      ],
    },
    {
      title: t("chats"),
      url: "/chat-records",
      icon: MessageSquare,
    },
  ];

  const isActive = (href: string) => location.pathname === href;

  return (
      <SidebarProvider>
        <Sidebar variant="inset" className="bg-gradient-sidebar border-sidebar-border">
          <SidebarHeader className="border-b border-sidebar-border/50 bg-sidebar/50 backdrop-blur-sm">
            <SidebarMenu>
              <SidebarMenuItem>
                <div className="flex items-center px-2 py-3">
                  <div className="relative">
                    <Bot className="h-8 w-8 text-sidebar-primary sidebar-icon-glow" />
                    <div className="absolute -inset-1 rounded-full bg-sidebar-primary/20 blur-sm"></div>
                  </div>
                  <div className="ml-3 flex items-center">
                  <span className="text-xl font-bold sidebar-brand">
                    {t("aiAssistant")}
                  </span>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="ml-2 p-1 hover:bg-sidebar-hover rounded-full transition-all duration-200">
                          <ChevronDown className="h-4 w-4 text-sidebar-accent-foreground" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                          align="start"
                          className="w-56 bg-sidebar border-sidebar-border shadow-elegant z-50 backdrop-blur-sm"
                      >
                        {projects.map((project) => (
                            <DropdownMenuItem
                                key={project.id}
                                onClick={() => setCurrentProject(project)}
                                className={
                                  currentProject?.id === project.id
                                      ? "bg-sidebar-accent text-sidebar-accent-foreground font-medium"
                                      : "hover:bg-sidebar-hover"
                                }
                            >
                              {project.name}
                            </DropdownMenuItem>
                        ))}
                        <DropdownMenuSeparator className="bg-sidebar-border" />
                        <DropdownMenuItem onClick={() => navigate("/onboarding-starter")} className="hover:bg-sidebar-hover text-sidebar-accent-foreground">
                          {t("newProject")}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarHeader>
          <div className="sidebar-divider"></div>
          <SidebarContent className="px-2">
            <SidebarGroup>
              <SidebarGroupContent>
                <SidebarMenu className="space-y-2">
                  {menuData.map((item) => (
                      <Collapsible
                          key={item.title}
                          asChild
                          defaultOpen={item.items?.some(subItem => isActive(subItem.url))}
                          className="group/collapsible"
                      >
                        <SidebarMenuItem className="sidebar-menu-item">
                          <CollapsibleTrigger asChild>
                            <SidebarMenuButton
                                tooltip={item.title}
                                isActive={item.url ? isActive(item.url) : false}
                                asChild={!!item.url}
                                className="rounded-lg mx-1 hover:bg-sidebar-hover transition-all duration-200 group h-12 text-base font-normal !justify-start !pl-3"
                            >
                              {item.url ? (
                                  <Link to={item.url} className="flex items-center w-full !justify-start">
                                    <item.icon className="h-5 w-5 text-sidebar-accent-foreground group-hover:text-sidebar-primary transition-colors duration-200 !mr-3 !ml-0" />
                                    <span className={`text-base ${item.url && isActive(item.url) ? 'font-semibold' : 'font-normal'}`}>{item.title}</span>
                                  </Link>
                              ) : (
                                  <div className="flex items-center w-full !justify-start">
                                    <item.icon className="h-5 w-5 text-sidebar-accent-foreground group-hover:text-sidebar-primary transition-colors duration-200 !mr-3 !ml-0" />
                                    <span className={`text-base flex-1 text-left ${!item.url && item.items?.some(subItem => isActive(subItem.url)) ? 'font-semibold' : 'font-normal'}`}>{item.title}</span>
                                    {item.items && <ChevronDown className="h-4 w-4 text-sidebar-muted-foreground transition-transform duration-200 group-data-[state=open]/collapsible:rotate-180" />}
                                  </div>
                              )}
                            </SidebarMenuButton>
                          </CollapsibleTrigger>
                          {item.items && (
                              <CollapsibleContent>
                                <SidebarMenuSub className="ml-6 mt-2 space-y-2">
                                  {item.items.map((subItem) => (
                                      <SidebarMenuSubItem key={subItem.title}>
                                        <SidebarMenuSubButton
                                            asChild
                                            isActive={isActive(subItem.url)}
                                            className="rounded-lg hover:bg-sidebar-hover transition-all duration-200 group h-10 !justify-start !pl-3"
                                        >
                                          <Link to={subItem.url} className="flex items-center w-full !justify-start">
                                            <subItem.icon className="h-4 w-4 text-sidebar-muted-foreground group-hover:text-sidebar-accent-foreground transition-colors duration-200 !mr-3 !ml-0" />
                                            <span className={`text-sm ${isActive(subItem.url) ? 'font-semibold' : 'font-normal'}`}>{subItem.title}</span>
                                          </Link>
                                        </SidebarMenuSubButton>
                                      </SidebarMenuSubItem>
                                  ))}
                                </SidebarMenuSub>
                              </CollapsibleContent>
                          )}
                        </SidebarMenuItem>
                      </Collapsible>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </SidebarContent>
          <div className="sidebar-divider"></div>
          <SidebarFooter className="border-t border-sidebar-border/50 bg-sidebar/30 backdrop-blur-sm">
            {isLoading ? (
                // 加载状态
                <div className="flex items-center p-3 mx-2 rounded-lg">
                  <div className="w-10 h-10 bg-sidebar-muted rounded-full flex items-center justify-center animate-pulse">
                    <User className="w-5 h-5 text-sidebar-muted-foreground" />
                  </div>
                  <div className="ml-3 flex-1">
                    <div className="h-4 bg-sidebar-muted rounded animate-pulse mb-2 w-20"></div>
                    <div className="h-3 bg-sidebar-muted/60 rounded animate-pulse w-16"></div>
                  </div>
                </div>
            ) : (
                // 用户信息下拉菜单
                <SidebarMenu>
                  <SidebarMenuItem>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <SidebarMenuButton
                            size="lg"
                            className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground ml-1 mr-2 rounded-lg hover:bg-sidebar-hover transition-all duration-200 group h-14"
                        >
                          <div className="relative w-9 h-9 bg-gradient-primary rounded-full flex items-center justify-center text-white text-sm font-bold shadow-elegant">
                            {user?.name ? (
                                user.name.charAt(0).toUpperCase()
                            ) : (
                                <User className="w-4 h-4" />
                            )}
                            <div className="absolute -inset-0.5 rounded-full bg-sidebar-primary/20 blur-sm -z-10"></div>
                          </div>
                          <div className="grid flex-1 text-left leading-tight ml-2">
                        <span className="truncate font-semibold text-sidebar-foreground text-base">
                          {user?.name || "User"}
                        </span>
                          </div>
                          <ChevronDown className="ml-auto size-5 text-sidebar-muted-foreground group-hover:text-sidebar-accent-foreground transition-colors duration-200" />
                        </SidebarMenuButton>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                          className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg bg-sidebar border-sidebar-border shadow-elegant backdrop-blur-sm"
                          side="bottom"
                          align="end"
                          sideOffset={4}
                      >
                        <DropdownMenuItem className="flex items-center hover:bg-sidebar-hover py-3">
                          <User className="mr-3 h-5 w-5 text-sidebar-accent-foreground" />
                          <div>
                            <div className="font-semibold text-sidebar-foreground text-base">{user?.name || "User"}</div>
                          </div>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator className="bg-sidebar-border" />
                        <DropdownMenuItem
                            onClick={() => navigate("/account")}
                            className="flex items-center hover:bg-sidebar-hover transition-colors duration-200 py-2.5"
                        >
                          <Settings className="mr-3 h-5 w-5 text-sidebar-accent-foreground" />
                          <span className="text-sidebar-foreground text-base">{t("account")}</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                            onClick={() => navigate("/billing")}
                            className="flex items-center hover:bg-sidebar-hover transition-colors duration-200 py-2.5"
                        >
                          <CreditCard className="mr-3 h-5 w-5 text-sidebar-accent-foreground" />
                          <span className="text-sidebar-foreground text-base">{t("billing")}</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                            onClick={logout}
                            className="text-destructive hover:text-destructive-foreground hover:bg-destructive/10 transition-colors duration-200 py-2.5"
                        >
                          <LogOut className="mr-3 h-5 w-5" />
                          <span className="text-base">{t("logout")}</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </SidebarMenuItem>
                </SidebarMenu>
            )}
          </SidebarFooter>
          <SidebarRail />
        </Sidebar>
        <SidebarInset>
          <header className="flex mb-4 h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12 border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1 hover:bg-accent rounded-lg transition-colors duration-200" />
            </div>
          </header>
          <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
            {children}
          </div>
        </SidebarInset>
      </SidebarProvider>
  );
};