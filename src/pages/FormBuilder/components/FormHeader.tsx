import { useState, useRef, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import { Upload, X, Image, Edit3, Check, X as XIcon } from "lucide-react";

interface FormHeaderProps {
  formTitle: string;
  formDescription: string;
  formLogo: string | null;
  onUpdateFormTitle: (title: string) => void;
  onUpdateFormDescription: (description: string) => void;
  onUpdateFormLogo: (logo: string | null) => void;
  disabled?: boolean;
}

export const FormHeader = ({
  formTitle,
  formDescription,
  formLogo,
  onUpdateFormTitle,
  onUpdateFormDescription,
  onUpdateFormLogo,
  disabled = false,
}: FormHeaderProps) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [isEditingDescription, setIsEditingDescription] = useState(false);
  const [tempTitle, setTempTitle] = useState(formTitle);
  const [tempDescription, setTempDescription] = useState(formDescription);
  const [isDragging, setIsDragging] = useState(false);

  // 同步外部状态变化
  useEffect(() => {
    setTempTitle(formTitle);
  }, [formTitle]);

  useEffect(() => {
    setTempDescription(formDescription);
  }, [formDescription]);

  // 验证文件类型和大小
  const validateFile = (file: File): boolean => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!allowedTypes.includes(file.type)) {
      toast({
        variant: "destructive",
        title: t("logoUploadError"),
        description: t("logoFormatError"),
      });
      return false;
    }

    const maxSize = 2 * 1024 * 1024;
    if (file.size > maxSize) {
      toast({
        variant: "destructive",
        title: t("logoUploadError"),
        description: t("logoSizeError"),
      });
      return false;
    }

    return true;
  };

  // 将文件转换为 base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        if (typeof reader.result === 'string') {
          resolve(reader.result);
        } else {
          reject(new Error('Failed to read file'));
        }
      };
      reader.onerror = () => reject(reader.error);
      reader.readAsDataURL(file);
    });
  };

  // 处理文件上传
  const handleFileUpload = async (file: File) => {
    if (!validateFile(file)) {
      return;
    }

    try {
      const base64 = await fileToBase64(file);
      onUpdateFormLogo(base64);
      toast({
        title: t("uploadSuccess"),
        description: t("logoUploadSuccess"),
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: t("logoUploadError"),
        description: error instanceof Error ? error.message : t("logoUploadError"),
      });
    }
  };

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 处理拖拽
  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    if (!disabled) {
      setIsDragging(true);
    }
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);
    
    if (disabled) return;

    const file = event.dataTransfer.files[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  // 标题编辑处理
  const handleTitleEdit = () => {
    if (disabled) return;
    setIsEditingTitle(true);
  };

  const handleTitleSave = () => {
    onUpdateFormTitle(tempTitle);
    setIsEditingTitle(false);
  };

  const handleTitleCancel = () => {
    setTempTitle(formTitle);
    setIsEditingTitle(false);
  };

  // 描述编辑处理
  const handleDescriptionEdit = () => {
    if (disabled) return;
    setIsEditingDescription(true);
  };

  const handleDescriptionSave = () => {
    onUpdateFormDescription(tempDescription);
    setIsEditingDescription(false);
  };

  const handleDescriptionCancel = () => {
    setTempDescription(formDescription);
    setIsEditingDescription(false);
  };

  // Logo 上传点击
  const handleLogoUploadClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 移除 Logo
  const handleLogoRemove = () => {
    onUpdateFormLogo(null);
  };

  return (
    <div className="mb-8 text-center space-y-4">
      {/* Logo 区域 */}
      <div className="flex justify-center relative">
        <div className="relative">
          <div
            className={`
              w-20 h-20 border-2 border-dashed rounded-lg flex items-center justify-center
              ${isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300'}
              ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:border-gray-400'}
              ${formLogo ? 'border-solid' : ''}
            `}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={handleLogoUploadClick}
          >
            {formLogo ? (
              <img
                src={formLogo}
                alt="Form Logo"
                className="w-full h-full object-cover rounded-lg"
              />
            ) : (
              <div className="text-center">
                <Image className="w-8 h-8 text-gray-400 mx-auto mb-1" />
                <span className="text-xs text-gray-500">{t("uploadLogo")}</span>
              </div>
            )}
          </div>

          {/* 移除按钮 - 调整位置确保不影响居中 */}
          {formLogo && !disabled && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleLogoRemove();
              }}
              className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors z-10"
            >
              <X className="w-3 h-3" />
            </button>
          )}
        </div>
      </div>

      {/* 标题区域 */}
      <div className="relative group text-center">
        {isEditingTitle ? (
          <div className="flex items-center justify-center space-x-2">
            <Input
              value={tempTitle}
              onChange={(e) => setTempTitle(e.target.value)}
              className="text-2xl font-bold text-center max-w-md"
              placeholder={t("formTitlePlaceholder")}
              autoFocus
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleTitleSave();
                } else if (e.key === 'Escape') {
                  handleTitleCancel();
                }
              }}
            />
            <Button size="sm" onClick={handleTitleSave}>
              <Check className="w-4 h-4" />
            </Button>
            <Button size="sm" variant="outline" onClick={handleTitleCancel}>
              <XIcon className="w-4 h-4" />
            </Button>
          </div>
        ) : (
          <div className="flex items-center justify-center space-x-2">
            <h1 className="text-2xl font-bold text-gray-900">
              {formTitle || t('formTitleDefault')}
            </h1>
            {!disabled && (
              <Button
                size="sm"
                variant="ghost"
                className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={handleTitleEdit}
              >
                <Edit3 className="w-4 h-4 text-gray-400" />
              </Button>
            )}
          </div>
        )}
      </div>

      {/* 描述区域 */}
      <div className="relative group text-center">
        {isEditingDescription ? (
          <div className="flex flex-col items-center space-y-2">
            <Textarea
              value={tempDescription}
              onChange={(e) => setTempDescription(e.target.value)}
              className="text-center max-w-md resize-none"
              placeholder={t("formDescriptionPlaceholder")}
              rows={2}
              autoFocus
              onKeyDown={(e) => {
                if (e.key === 'Enter' && e.ctrlKey) {
                  handleDescriptionSave();
                } else if (e.key === 'Escape') {
                  handleDescriptionCancel();
                }
              }}
            />
            <div className="flex space-x-2">
              <Button size="sm" onClick={handleDescriptionSave}>
                <Check className="w-4 h-4" />
              </Button>
              <Button size="sm" variant="outline" onClick={handleDescriptionCancel}>
                <XIcon className="w-4 h-4" />
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center space-x-2">
            <p className="text-gray-600 max-w-md">
              {formDescription || t('fillFollowingInfo')}
            </p>
            {!disabled && (
              <Button
                size="sm"
                variant="ghost"
                className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={handleDescriptionEdit}
              >
                <Edit3 className="w-4 h-4 text-gray-400" />
              </Button>
            )}
          </div>
        )}
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/jpg,image/png"
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled}
      />
    </div>
  );
};
