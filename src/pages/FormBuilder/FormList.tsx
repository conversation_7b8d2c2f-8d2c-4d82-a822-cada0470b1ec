import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DeleteConfirm } from "@/components/ui/delete-confirm";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import {
  Calendar,
  ChevronDown,
  Copy,
  Edit,
  Eye,
  FileText,
  Loader2,
  Plus,
  Search,
  Trash2,
  ListOrdered,
  CheckCircle,
  Circle,
} from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { formApi } from "../../services/api/form";
import { FormConfig, TemplateResponse } from "./types/form-types";
import { FormSubmissionsModal } from "./components/FormSubmissionsModal";

interface FormListProps {
  projectId: string;
  toDesigner: () => void;
}

export const FormList = ({ projectId, toDesigner }: FormListProps) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { t } = useTranslation();

  const [forms, setForms] = useState<FormConfig[]>([]);
  const [templates, setTemplates] = useState<TemplateResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [selectedFormId, setSelectedFormId] = useState<string | null>(null);
  const [showSubmissionsModal, setShowSubmissionsModal] = useState(false);

  useEffect(() => {
    if (projectId) {
      loadForms();
      loadTemplates();
    }
  }, [projectId]);

  const loadTemplates = async () => {
    try {
      const formTemplates = await formApi.getFormTemplates();
      setTemplates(formTemplates);
    } catch (err) {
      console.error(err);
    }
  };

  const loadForms = async () => {
    setLoading(true);
    setError(null);

    try {
      const projectForms = await formApi.getProjectForms(projectId);
      setForms(projectForms);
    } catch (err) {
      setError(err instanceof Error ? err.message : "加载表单列表失败");
    } finally {
      setLoading(false);
    }
  };

  const handleCreateForm = () => {
    toDesigner();
  };

  const handleCreateFromTemplate = async (template: TemplateResponse) => {
    try {
      let content;
      try {
        content = JSON.parse(template.content);
      } catch (e) {
        console.error("Failed to parse template content", e);
        return;
      }
      const templateConfig = {
        formId: "",
        title: template.name,
        description: template.description,
        logo: template.logo,
        ...content,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      await formApi.saveForm(projectId, templateConfig);
      toast({
        title: t("templateCreatedSuccessfully"),
        description: `${templateConfig.title}${t("templateCreated")}`,
      });
      loadForms(); // 重新加载列表
    } catch (error) {
      toast({
        variant: "destructive",
        title: t("templateCreationFailed"),
        description:
          error instanceof Error ? error.message : t("templateCreationFailed"),
      });
    }
  };

  const handleEditForm = (formId: string) => {
    navigate(`/form-builder?formId=${formId}`);
  };

  const handlePreviewForm = (formId: string) => {
    navigate(`/form-preview/${formId}`);
  };

  const handleDuplicateForm = async (form: FormConfig) => {
    try {
      await formApi.duplicateForm(form.formId);
      toast({
        title: t("copySuccess"),
        description: t("formCopiedSuccessfully"),
      });
      loadForms(); // 重新加载列表
    } catch (error) {
      toast({
        variant: "destructive",
        title: t("copyFailed"),
        description:
          error instanceof Error ? error.message : t("formCopyFailed"),
      });
    }
  };

  const handleSetFormActive = async (formId: string, currentActiveState: boolean) => {
    // 如果表单已经是激活状态，则不执行任何操作
    if (currentActiveState) return;

    try {
      await formApi.setActive(formId);
      toast({
        title: t("active"),
        description: t("saveConfig"),
      });
      loadForms(); // 重新加载列表以显示更新后的状态
    } catch (error) {
      toast({
        variant: "destructive",
        title: t("saveFailed"),
        description:
          error instanceof Error ? error.message : t("saveFailed"),
      });
    }
  };

  const handleDeleteForm = async (formId: string) => {
    try {
      await formApi.deleteForm(formId);
      toast({
        title: t("deleteSuccess"),
        description: t("formDeletedSuccessfully"),
      });
      loadForms(); // 重新加载列表
    } catch (error) {
      toast({
        variant: "destructive",
        title: t("deleteFailed"),
        description:
          error instanceof Error ? error.message : t("formDeleteFailed"),
      });
    }
  };

  const handleCopyShareUrl = (formId: string) => {
    const shareUrl = `${window.location.origin}/form/${formId}`;
    navigator.clipboard.writeText(shareUrl);
    toast({
      title: t("linkCopied"),
      description: t("shareLinkCopied"),
    });
  };

  // 过滤表单
  const filteredForms = forms.filter(
    (form) =>
      form.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      form.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div>
      <div className="space-y-6">
        {/* 头部操作栏 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <FileText className="mr-2 h-6 w-6" />
              {t("formManagement")}
            </h1>{" "}
            <p className="text-gray-600 mt-1">{t("createAndManageForms")}</p>
          </div>

          <div className="flex items-center space-x-2">
            <Button onClick={handleCreateForm}>
              <Plus className="w-4 h-4 mr-2" />
              {t("newForm")}
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  {t("template")}
                  <ChevronDown className="w-4 h-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                {templates.map((template) => (
                  <DropdownMenuItem
                    key={template.id}
                    onClick={() => handleCreateFromTemplate(template)}
                  >
                    {template.name}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* 搜索栏 */}
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder={t("searchForms")}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* 表单列表 */}
        {filteredForms.length === 0 ? (
          <Card>
            <CardContent className="py-12 text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FileText className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchQuery ? t("noFormsFound") : t("noFormsYet")}
              </h3>
              <p className="text-gray-600 mb-4">
                {searchQuery ? t("tryDifferentSearch") : t("createFirstForm")}
              </p>
              {!searchQuery && (
                <Button onClick={handleCreateForm}>
                  <Plus className="w-4 h-4 mr-2" />
                  {t("newForm")}
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredForms.map((form) => (
              <Card
                key={form.formId}
                className="hover:shadow-lg transition-shadow"
              >
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg line-clamp-2">
                        {form.title}
                      </CardTitle>
                      <div className="min-h-[40px]">
                        {form.description && (
                          <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                            {form.description}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* 表单统计 */}
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <div className="flex items-center space-x-1">
                      <FileText className="w-4 h-4" />
                      <span>
                        {form.components.length} {t("components")}
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>
                        {new Date(form.updatedAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEditForm(form.formId)}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>

                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handlePreviewForm(form.formId)}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>

                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDuplicateForm(form)}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>

                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setSelectedFormId(form.formId);
                          setShowSubmissionsModal(true);
                        }}
                      >
                        <ListOrdered className="w-4 h-4" />
                      </Button>
                    </div>

                    <div className="flex items-center space-x-2">
                      {/* 激活表单按钮 */}
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleSetFormActive(form.formId, !!form.active)}
                        className={form.active ? "text-green-600" : "text-gray-600"}
                      >
                        {form.active ? (
                          <CheckCircle className="w-4 h-4" />
                        ) : (
                          <Circle className="w-4 h-4" />
                        )}
                      </Button>

                      {/* <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleCopyShareUrl(form.formId)}
                      >
                        分享
                      </Button> */}

                      <DeleteConfirm
                        title={t("delete") + t("form")}
                        description={`${t("confirmDeleteForm")}"${
                          form.title
                        }"${t("deleteFormWarning")}`}
                        onConfirm={() => handleDeleteForm(form.formId)}
                      >
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </DeleteConfirm>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      <FormSubmissionsModal
        formId={selectedFormId || ""}
        open={showSubmissionsModal}
        onOpenChange={setShowSubmissionsModal}
      />
    </div>
  );
};
