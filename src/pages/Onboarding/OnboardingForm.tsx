import { OnboardingProgress } from "@/components/OnboardingProgress";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/context/AuthContext.tsx";
import { useOnboardingData } from "@/hooks/useOnboardingData";
import { FormDesigner } from "@/pages/FormBuilder/FormDesigner";
import { FormConfig } from "@/pages/FormBuilder/types/form-types";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

const OnboardingForm = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();
  const { formData, updateFormData } = useOnboardingData();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, isLoading]);

  const handleNext = () => {
    navigate("/onboarding-chatbot");
  };

  const handleBack = () => {
    navigate("/onboarding-starter");
  };

  const saveForm = async (formConfig: FormConfig) => {
    updateFormData(formConfig);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col">
      <OnboardingProgress currentStep={2} />

      {/* Scrollable content */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="w-full max-w-6xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Form Designer
            </h1>
            <p className="text-gray-600">
              Design your lead capture form for the chatbot
            </p>
          </div>

          <div className="bg-white rounded-xl shadow-lg h-[70vh] overflow-hidden">
            <FormDesigner formConfig={formData} showFormTemplate={true} disablePreview={true} saveForm={saveForm} />
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-between mt-8 pb-8">
            <Button variant="outline" onClick={handleBack}>
              Back
            </Button>
            <Button onClick={handleNext} className="px-8 py-2 h-10">
              Next
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OnboardingForm;
