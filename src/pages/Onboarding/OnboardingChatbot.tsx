import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { X, Upload, Plus, GripVertical } from "lucide-react";
import { ChatPreview } from "@/components/ChatPreview";
import { useOnboardingData } from "@/hooks/useOnboardingData";
import { OnboardingProgress } from "@/components/OnboardingProgress";
import { useAuth } from "@/context/AuthContext.tsx";

const OnboardingChatbot = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();
  const { data, updateData } = useOnboardingData();
  const [brandName, setBrandName] = useState(data.brandName);
  const [brandColor, setBrandColor] = useState(data.brandColor);
  const [logo, setLogo] = useState(data.logo);
  const [welcomeMessage, setWelcomeMessage] = useState(data.welcomeMessage);
  const [suggestedQuestionsEnabled, setSuggestedQuestionsEnabled] = useState(
    data.suggestedQuestionsEnabled
  );
  const [suggestedQuestions, setSuggestedQuestions] = useState(
    data.suggestedQuestions
  );
  const [draggedIndex, setDraggedIndex] = useState(null);
  const [dragOverIndex, setDragOverIndex] = useState(null);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate("/login");
    }
    updateData({
      brandName,
      brandColor,
      logo,
      welcomeMessage,
      suggestedQuestions,
      suggestedQuestionsEnabled,
    });
  }, [
    isAuthenticated,
    isLoading,
    brandName,
    brandColor,
    logo,
    welcomeMessage,
    suggestedQuestions,
    suggestedQuestionsEnabled,
  ]);

  const handleNext = () => {
    navigate("/onboarding-final");
  };

  const handleBack = () => {
    navigate("/onboarding-form");
  };

  const removeSuggestedQuestion = (index) => {
    setSuggestedQuestions((prev) => prev.filter((_, i) => i !== index));
  };

  const addSuggestedQuestion = () => {
    setSuggestedQuestions((prev) => [...prev, ""]);
  };

  const handleLogoUpload = (event) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result;
        if (typeof result === "string") {
          setLogo(result);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleDragStart = (e, index) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = "move";
    e.dataTransfer.setData("text/plain", index.toString());

    // 找到当前拖拽的整个item容器
    const draggedElement = e.target.closest(".draggable-item");
    if (draggedElement) {
      e.dataTransfer.setDragImage(draggedElement, 0, 0);
    }
  };

  const handleDragOver = (e, index) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
    setDragOverIndex(index);
  };

  const handleDragLeave = () => {
    setDragOverIndex(null);
  };

  const handleDrop = (e, dropIndex) => {
    e.preventDefault();
    setDragOverIndex(null);

    if (draggedIndex === null || draggedIndex === dropIndex) {
      setDraggedIndex(null);
      return;
    }

    const newQuestions = [...suggestedQuestions];
    const draggedItem = newQuestions[draggedIndex];

    // Remove the dragged item
    newQuestions.splice(draggedIndex, 1);

    // Insert at new position
    const insertIndex = draggedIndex < dropIndex ? dropIndex - 1 : dropIndex;
    newQuestions.splice(insertIndex, 0, draggedItem);

    setSuggestedQuestions(newQuestions);
    setDraggedIndex(null);
  };

  const handleDragEnd = () => {
    setDraggedIndex(null);
    setDragOverIndex(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col">
      <OnboardingProgress currentStep={3} />

      {/* Scrollable content */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="w-full max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 min-h-[600px]">
            {/* Left side - Form */}
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                Customize your AI Agent
              </h1>
              <p className="text-lg text-gray-600 mb-8">
                We tried to match your website with your colors, logos, and
                brand personality. Customize it below:
              </p>

              <div className="space-y-6">
                {/* Logo and Brand Info */}
                <div className="flex items-start space-x-6">
                  {/* Logo */}
                  <div>
                    <Label className="text-sm font-medium mb-2 block">
                      Logo
                    </Label>
                    <input
                      type="file"
                      id="logo-upload"
                      accept="image/*"
                      onChange={handleLogoUpload}
                      className="hidden"
                    />
                    <label htmlFor="logo-upload" className="cursor-pointer">
                      <div className="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden hover:bg-gray-300 transition-colors border-2 border-dashed border-gray-300 hover:border-primary">
                        {logo ? (
                          <img
                            src={logo}
                            alt="Logo"
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="text-center">
                            <Upload className="w-6 h-6 text-gray-400 mx-auto mb-1" />
                            <span className="text-xs text-gray-500">
                              Upload
                            </span>
                          </div>
                        )}
                      </div>
                    </label>
                  </div>

                  {/* Brand name and color */}
                  <div className="flex-1 space-y-4">
                    <div>
                      <Label
                        htmlFor="brandName"
                        className="text-sm font-medium mb-2 block"
                      >
                        Brand name
                      </Label>
                      <Input
                        id="brandName"
                        value={brandName}
                        onChange={(e) => setBrandName(e.target.value)}
                        className="max-w-xs"
                      />
                    </div>

                    <div>
                      <Label
                        htmlFor="brandColor"
                        className="text-sm font-medium mb-2 block"
                      >
                        Brand color
                      </Label>
                      <div className="flex items-center space-x-3">
                        <Input
                          id="brandColor"
                          value={brandColor}
                          onChange={(e) => setBrandColor(e.target.value)}
                          className="max-w-xs"
                        />
                        <input
                          type="color"
                          value={brandColor}
                          onChange={(e) => setBrandColor(e.target.value)}
                          className="w-8 h-8 rounded border cursor-pointer"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Welcome message */}
                <div>
                  <Label
                    htmlFor="welcomeMessage"
                    className="text-sm font-medium mb-2 block"
                  >
                    Welcome message
                  </Label>
                  <Textarea
                    id="welcomeMessage"
                    value={welcomeMessage}
                    onChange={(e) => setWelcomeMessage(e.target.value)}
                    rows={4}
                    className="resize-none"
                  />
                </div>

                {/* Suggested questions */}
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <Label className="text-sm font-medium">
                      Suggested questions
                    </Label>
                    <Switch
                      checked={suggestedQuestionsEnabled}
                      onCheckedChange={setSuggestedQuestionsEnabled}
                    />
                  </div>
                  <p className="text-sm text-gray-600 mb-4">
                    Help visitors start a conversation by providing quick,
                    one-click questions.
                  </p>

                  {suggestedQuestionsEnabled && (
                    <div className="space-y-2">
                      {suggestedQuestions.map((question, index) => (
                        <div
                          key={index}
                          className={`flex items-center space-x-2 p-2 rounded transition-colors ${
                            dragOverIndex === index
                              ? "bg-blue-50 border-2 border-blue-200"
                              : draggedIndex === index
                              ? "opacity-50"
                              : ""
                          }`}
                          draggable
                          onDragStart={(e) => handleDragStart(e, index)}
                          onDragOver={(e) => handleDragOver(e, index)}
                          onDragLeave={handleDragLeave}
                          onDrop={(e) => handleDrop(e, index)}
                          onDragEnd={handleDragEnd}
                        >
                          <GripVertical className="w-4 h-4 text-gray-400 cursor-grab hover:text-gray-600 active:cursor-grabbing" />
                          <Input
                            value={question}
                            onChange={(e) => {
                              const newQuestions = [...suggestedQuestions];
                              newQuestions[index] = e.target.value;
                              setSuggestedQuestions(newQuestions);
                            }}
                            className="flex-1"
                            placeholder="Enter a suggested question..."
                          />
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeSuggestedQuestion(index)}
                            className="hover:bg-red-50 hover:text-red-600"
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>
                      ))}

                      {/* Add Question Button */}
                      <div className="flex justify-center mt-3">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={addSuggestedQuestion}
                          className="border-dashed border-2 hover:border-solid hover:bg-gray-50"
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Add Question
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Right side - Preview with increased height */}
            <div className="flex items-start justify-center">
              <div style={{ height: "700px", width: "400px" }}>
                <ChatPreview
                  assistantName={brandName}
                  welcomeMessage={welcomeMessage}
                  color={brandColor}
                  suggestedQuestions={
                    suggestedQuestionsEnabled ? suggestedQuestions : []
                  }
                  showInput={false}
                  compact={false}
                  className="h-full w-full"
                  logo={logo}
                  notCreateSession={true}
                />
              </div>
            </div>
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-between mt-12 pb-8">
            <Button variant="outline" onClick={handleBack}>
              Back
            </Button>
            <Button onClick={handleNext} className="px-8 py-2 h-10">
              Next
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OnboardingChatbot;
