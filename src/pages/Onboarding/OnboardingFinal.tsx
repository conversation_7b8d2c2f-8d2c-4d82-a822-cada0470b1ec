import { ChatPreview } from "@/components/ChatPreview";
import { OnboardingProgress } from "@/components/OnboardingProgress";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/context/AuthContext.tsx";
import { useProject } from "@/context/ProjectContext";
import { useToast } from "@/hooks/use-toast";
import { useOnboardingData } from "@/hooks/useOnboardingData";
import { useTranslation } from "@/hooks/useTranslation";
import { createProject } from "@/services/api";
import { formApi } from "@/services/api/form";
import { generateEmbedCode } from "@/utils/embedCode";
import { Check, Copy, Loader2 } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { v4 as uuidv4 } from "uuid";

const OnboardingFinal = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const { refreshProjects, setCurrentProject } = useProject();
  const navigate = useNavigate();
  const { data, formData } = useOnboardingData();
  const { toast } = useToast();
  const [welcomeMessage] = useState(data.welcomeMessage);
  const [isCreating, setIsCreating] = useState(false);
  const { t } = useTranslation();
  const [isCopied, setIsCopied] = useState(false);
  const [id] = useState(uuidv4());

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, isLoading, navigate]);

  const handleStartNow = async () => {
    setIsCreating(true);

    try {
      const projectData = {
        projectId: id,
        name: data.brandName,
        website: data.websiteUrl,
        settings: {
          logo: data.logo || "",
          name: data.brandName,
          color: data.brandColor,
          welcomeMsg: welcomeMessage,
          suggestedEnable: data.suggestedQuestionsEnabled,
          suggestedQuestions: data.suggestedQuestions,
        },
      };

      const response = await createProject(projectData);

      formData.active = true;
      await formApi.saveForm(id, formData);

      toast({
        title: t("createProjectSuccess"),
        description: `${t("project")} "${response.name}" ${t("createSuccess")}`,
      });

      // 刷新项目列表并设置新创建的项目为当前项目
      await refreshProjects();
      setCurrentProject(response);

      // 清除onboarding数据
      localStorage.removeItem("onboarding_data");

      navigate("/dashboard");
    } catch (error) {
      console.error("Failed to create project:", error);
      toast({
        variant: "destructive",
        title: t("createProjectFailed"),
        description: error instanceof Error ? error.message : t("createFailed"),
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleBack = () => {
    navigate("/onboarding-chatbot");
  };

  const embedCode = generateEmbedCode({
    projectId: id,
  });
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(embedCode);
      setIsCopied(true);

      // 2秒后重置状态
      setTimeout(() => {
        setIsCopied(false);
      }, 2000);
    } catch (error) {
      console.error("复制失败:", error);
      // 降级方案：使用 document.execCommand
      try {
        const textArea = document.createElement("textarea");
        textArea.value = embedCode;
        textArea.style.position = "fixed";
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand("copy");
        document.body.removeChild(textArea);

        setIsCopied(true);
        setTimeout(() => {
          setIsCopied(false);
        }, 2000);
      } catch (fallbackError) {
        console.error("降级复制方案也失败:", fallbackError);
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col">
      <OnboardingProgress currentStep={4} />

      {/* Scrollable content */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="w-full max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 min-h-[600px]">
            {/* Left side - Chat Preview with increased height */}
            <div className="flex items-start justify-center">
              <div style={{ height: "700px", width: "400px" }}>
                <ChatPreview
                  assistantName={data.brandName}
                  welcomeMessage={welcomeMessage}
                  color={data.brandColor}
                  suggestedQuestions={
                    data.suggestedQuestionsEnabled
                      ? data.suggestedQuestions
                      : []
                  }
                  showInput={true}
                  compact={false}
                  className="h-full w-full"
                  logo={data.logo}
                />
              </div>
            </div>

            {/* Right side - Instructions & Configuration */}
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-2xl">🎉</span>
                <h1 className="text-3xl font-bold text-gray-900">
                  Your AI Agent is ready!
                </h1>
              </div>

              <p className="text-gray-600 mb-4">
                When you're satisfied, copy the code below and paste it before
                the closing &lt;/body&gt; tag on your website to activate it.
              </p>

              {/* Code snippet */}
              <div className="relative bg-gray-50 border border-gray-200 rounded-lg overflow-hidden mb-4">
                {/* 代码头部 */}
                <div className="flex items-center justify-between px-4 py-2 bg-gray-100 border-b border-gray-200">
                  <span className="text-xs font-medium text-gray-600">
                    Embed Code
                  </span>
                  <button
                    onClick={copyToClipboard}
                    className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded transition-all duration-200 ${
                      isCopied
                        ? "bg-green-100 text-green-800 border border-green-200"
                        : "bg-white text-gray-700 border border-gray-300 hover:bg-gray-50"
                    }`}
                    title={isCopied ? "Copied!" : "Copy"}
                  >
                    {isCopied ? (
                      <>
                        <Check className="h-3 w-3 mr-1" />
                        Copied!
                      </>
                    ) : (
                      <>
                        <Copy className="h-3 w-3 mr-1" />
                        Copy
                      </>
                    )}
                  </button>
                </div>

                {/* 代码内容 */}
                <div className="p-4">
                  <pre className="text-sm font-mono text-gray-800 whitespace-pre-wrap break-all">
                    {embedCode}
                  </pre>
                </div>
              </div>

              {data.suggestedQuestionsEnabled &&
                data.suggestedQuestions.length > 0 && (
                  <>
                    <p className="text-gray-600 mb-4">
                      Go ahead, test your AI agent now. Try the examples below:
                    </p>

                    {/* Test examples */}
                    <div className="space-y-2 mb-8">
                      <p className="text-sm font-medium text-gray-900 mb-3">
                        Test with these examples:
                      </p>
                      {data.suggestedQuestions.map((question, index) => (
                        <button
                          key={index}
                          className="block w-full text-left px-4 py-3 bg-white border rounded-lg hover:bg-gray-50 text-sm"
                          onClick={() =>
                            navigator.clipboard.writeText(question)
                          }
                        >
                          <div className="flex items-center justify-between">
                            <span>{question}</span>
                            <Copy className="w-4 h-4 text-gray-400" />
                          </div>
                        </button>
                      ))}
                    </div>
                  </>
                )}
            </div>
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-between mt-12 pb-8">
            <Button
              variant="outline"
              onClick={handleBack}
              disabled={isCreating}
            >
              Back
            </Button>
            <Button
              onClick={handleStartNow}
              disabled={isCreating}
              className="px-8 py-2 h-10"
            >
              {isCreating ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                "Start Now"
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OnboardingFinal;
