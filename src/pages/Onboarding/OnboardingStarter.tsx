import {useState, useEffect, useRef} from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { OnboardingProgress } from '@/components/OnboardingProgress';
import { useOnboardingData } from '@/hooks/useOnboardingData';
import {useAuth} from "@/context/AuthContext.tsx";

const OnboardingStarter = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();
  const { data, updateData } = useOnboardingData();
  const [websiteUrl, setWebsiteUrl] = useState(data.websiteUrl);
  const hasInitialized = useRef(false);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, isLoading, navigate]);

  // 分离初始化逻辑
  useEffect(() => {
    if (!hasInitialized.current && data.websiteUrl) {
      setWebsiteUrl(data.websiteUrl);
      hasInitialized.current = true;
    }
  }, [data.websiteUrl]);

  const handleNext = () => {
    updateData({ websiteUrl });

    // 可选：添加小延迟确保数据已保存
    setTimeout(() => {
      navigate('/onboarding-form');
    }, 100);
  };

  const handleSkip = () => {
    navigate('/dashboard');
  };

  return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col">
        <OnboardingProgress currentStep={1} />

        {/* Scrollable content */}
        <div className="flex-1 overflow-y-auto flex items-center justify-center p-4">
          <div className="w-full max-w-2xl mx-auto">
            <div className="text-center">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                What's your website address?
              </h1>
              <p className="text-lg text-gray-600 mb-12 max-w-lg mx-auto">
                Provide your website URL to help train your AI agent. We'll start by pulling in
                key data to get your chatbot ready in minutes.
              </p>

              <div className="max-w-md mx-auto mb-8">
                <Input
                    type="url"
                    placeholder="Enter your website address"
                    value={websiteUrl}
                    onChange={(e) => setWebsiteUrl(e.target.value)}
                    className="h-12 text-base"
                />
              </div>

              <div className="flex items-center justify-between">
                <Button
                    variant="ghost"
                    onClick={handleSkip}
                    className="text-gray-500"
                >
                  You can complete this step later
                </Button>
                <Button
                    onClick={handleNext}
                    className="px-8 py-2 h-10"
                >
                  Next
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
  );
};

export default OnboardingStarter;